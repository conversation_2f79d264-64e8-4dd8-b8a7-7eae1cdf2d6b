<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻展示</title>
    <!-- 引入swiper -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            overflow: hidden;
        }

        .swiper-container {
            width: 100%;
            height: auto;
        }

        .swiper-slide {
            flex-shrink: 0;
        }

        .image-item {
            width: 100%;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
            height: 300px;
        }

        .image-item:hover .slide-image {
            transform: scale(1.2);
        }

        .slide-image {
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
            transition: all 0.3s ease-in-out;
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 30px 15px 15px;
        }

        .overlay-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .overlay-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        /* 蓝色信息卡片 */
        .blue-info-card {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            background: #0078d4;
            color: white;
            padding: 15px 20px;
            text-align: left;
            width: 100%;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .blue-info-card::after {
            content: '';
            position: absolute;
            bottom: 0px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #FFFFFF;
        }

        .blue-info-card .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .blue-info-card .card-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .image-item.active .blue-info-card {
            opacity: 1;
        }

        .content-area {
            border-width: 1px 0 1px 0;
            border-style: solid;
            border-color: #e5e5e5;
            min-height: 120px;
            padding: 20px 0;
            margin-top: 20px;
        }

        .slide-content {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            display: none;
            padding: 20px;
            background: white;
            margin-top: 20px;
            border-top: 1px solid #e5e5e5;
        }

        .swiper-slide-active .slide-content {
            display: block;
        }

        .title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #333;
        }

        /* Swiper导航按钮 */
        .swiper-button-prev,
        .swiper-button-next {
            width: 40px;
            height: 40px;
            margin-top: -20px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .swiper-button-prev {
            left: -60px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMjQgMTJMMTYgMjBMMjQgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        }

        .swiper-button-next {
            right: -60px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMTYgMTJMMjQgMjBMMTYgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        }

        .swiper-button-prev:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwMDc4ZDQiLz4KPHBhdGggZD0iTTI0IDEyTDE2IDIwTDI0IDI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }

        .swiper-button-next:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwMDc4ZDQiLz4KPHBhdGggZD0iTTE2IDEyTDI0IDIwTDE2IDI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }

        /* 隐藏默认的箭头 */
        .swiper-button-prev::after,
        .swiper-button-next::after {
            display: none;
        }

        /* 隐藏分页圆点 */
        .swiper-pagination {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="title">新闻展示</div>

    <div style="position: relative; max-width: 1000px; margin: 0 auto;">
        <div class="container">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <!-- 第一张图片 -->
                    <div class="swiper-slide">
                        <div class="image-item">
                            <img class="slide-image" src="http://jzt_dev_2.china9.cn/images/blog/7.jpg" alt="" />
                            <div class="blue-info-card">
                                <div class="card-title">蒋季茂</div>
                                <div class="card-subtitle">市委副书记、市长</div>
                            </div>
                        </div>
                        <div class="slide-content">
                            区政府工作会议今天（2月8日）下午召开。区委副书记、区长蒋季茂出席，会议听取了各部门工作汇报，并对下一步工作进行了部署。会议要求，各部门要认真贯彻落实区委、区政府的决策部署，进一步提高工作效率，确保各项工作顺利推进。
                        </div>
                    </div>

                    <!-- 第二张图片 -->
                    <div class="swiper-slide">
                        <div class="image-item">
                            <img class="slide-image" src="http://jzt_dev_2.china9.cn/images/blog/6.jpg" alt="" />
                            <div class="blue-info-card">
                                <div class="card-title">工作会议</div>
                                <div class="card-subtitle">重要部署会议</div>
                            </div>
                        </div>
                        <div class="slide-content">
                            今日召开的重要工作会议，就当前各项重点工作进行了深入讨论和部署。与会人员就相关议题进行了充分交流，形成了一系列重要共识，为下一步工作指明了方向。
                        </div>
                    </div>

                    <!-- 第三张图片 -->
                    <div class="swiper-slide">
                        <div class="image-item">
                            <img class="slide-image" src="http://jzt_dev_2.china9.cn/images/blog/5.jpg" alt="" />
                            <div class="blue-info-card">
                                <div class="card-title">学习培训</div>
                                <div class="card-subtitle">专题学习会议</div>
                            </div>
                        </div>
                        <div class="slide-content">
                            专题学习会议在市政府会议室举行，全体工作人员参加了此次学习活动。会议围绕最新政策文件进行了深入学习和讨论，进一步提高了大家的理论水平和业务能力。
                        </div>
                    </div>

                    <!-- 第四张图片 -->
                    <div class="swiper-slide">
                        <div class="image-item">
                            <img class="slide-image" src="http://jzt_dev_2.china9.cn/images/blog/4.jpg" alt="" />
                            <div class="blue-info-card">
                                <div class="card-title">党建活动</div>
                                <div class="card-subtitle">主题教育实践</div>
                            </div>
                        </div>
                        <div class="slide-content">
                            党建主题教育实践活动深入开展，全体党员干部积极参与学习讨论，进一步增强了党性修养和理论水平，为推动各项工作提供了坚强的思想保证。
                        </div>
                    </div>

                    <!-- 第五张图片 -->
                    <div class="swiper-slide">
                        <div class="image-item">
                            <img class="slide-image" src="http://jzt_dev_2.china9.cn/images/blog/3.jpg" alt="" />
                            <div class="blue-info-card">
                                <div class="card-title">便民服务</div>
                                <div class="card-subtitle">为民办实事</div>
                            </div>
                        </div>
                        <div class="slide-content">
                            便民服务中心正式启用，为群众提供一站式服务，大大提高了办事效率。中心设置了多个服务窗口，涵盖了民政、社保、医保等多个领域，真正实现了让数据多跑路、让群众少跑腿。
                        </div>
                    </div>

                    <!-- 第六张图片 -->
                    <div class="swiper-slide">
                        <div class="image-item">
                            <img class="slide-image" src="http://jzt_dev_2.china9.cn/images/blog/2.jpg" alt="" />
                            <div class="blue-info-card">
                                <div class="card-title">招商引资</div>
                                <div class="card-subtitle">产业发展会议</div>
                            </div>
                        </div>
                        <div class="slide-content">
                            招商引资工作取得新突破，成功引进多个重点项目落户本区。这些项目的落地将为区域经济发展注入新的活力，预计将创造大量就业机会，促进产业结构优化升级。
                        </div>
                    </div>
                </div>

                <!-- 导航按钮 -->
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>


        </div>

        <script>


            // 初始化Swiper
            const swiper = new Swiper('.swiper-container', {
                slidesPerView: 3,
                spaceBetween: 20,
                centeredSlides: true,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'slide',
                speed: 600,
                slideToClickedSlide: true,
                on: {
                    slideChange: function () {
                        // 移除所有active类
                        document.querySelectorAll('.image-item').forEach(item => {
                            item.classList.remove('active');
                        });

                        // 添加active类到当前中心的slide
                        const activeSlide = document.querySelectorAll('.swiper-slide')[this.activeIndex];
                        if (activeSlide) {
                            const imageItem = activeSlide.querySelector('.image-item');
                            if (imageItem) {
                                imageItem.classList.add('active');
                            }
                        }
                    }
                }
            });

            // 初始化时设置第一个为active
            setTimeout(() => {
                const firstSlide = document.querySelector('.swiper-slide-active .image-item');
                if (firstSlide) {
                    firstSlide.classList.add('active');
                }
            }, 100);
        </script>
</body>

</html>