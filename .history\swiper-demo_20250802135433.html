<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .image-gallery {
            display: flex;
            gap: 2px;
            width: 100%;
        }

        .image-item {
            flex: 1;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            min-width: 0;
        }

        .image-item.active {
            border-color: #0078d4;
        }

        .image-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .slide-image {
            width: 100%;
            height: 200px;
            display: block;
            object-fit: cover;
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 30px 15px 15px;
        }

        .overlay-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .overlay-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .content-area {
            padding: 20px;
            background: white;
            min-height: 120px;
        }

        .content-description {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            display: none;
        }

        .content-description.active {
            display: block;
        }

        .title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #333;
        }

        /* 导航按钮 */
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .nav-prev {
            left: -60px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMjQgMTJMMTYgMjBMMjQgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        }

        .nav-next {
            right: -60px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMTYgMTJMMjQgMjBMMTYgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        }

        .nav-button:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .nav-prev:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwMDc4ZDQiLz4KPHBhdGggZD0iTTI0IDEyTDE2IDIwTDI0IDI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }

        .nav-next:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwMDc4ZDQiLz4KPHBhdGggZD0iTTE2IDEyTDI0IDIwTDE2IDI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }
    </style>
</head>
<body>
    <div class="title">新闻展示</div>

    <div style="position: relative; max-width: 1000px; margin: 0 auto;">
        <!-- 导航按钮 -->
        <button class="nav-button nav-prev" onclick="previousImage()"></button>
        <button class="nav-button nav-next" onclick="nextImage()"></button>

        <div class="container">
            <div class="image-gallery">
                <!-- 第一张图片 -->
                <div class="image-item active" onclick="selectImage(0)">
                    <svg class="slide-image" viewBox="0 0 800 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="800" height="300" fill="#e74c3c"/>
                        <text x="400" y="150" text-anchor="middle" dominant-baseline="middle" fill="white" font-size="36" font-family="Microsoft YaHei">人民政协</text>
                    </svg>
                    <div class="image-overlay">
                        <div class="overlay-title">蒋季茂</div>
                        <div class="overlay-subtitle">市委副书记、市长</div>
                    </div>
                </div>

                <!-- 第二张图片 -->
                <div class="image-item" onclick="selectImage(1)">
                    <svg class="slide-image" viewBox="0 0 800 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="800" height="300" fill="#3498db"/>
                        <text x="400" y="150" text-anchor="middle" dominant-baseline="middle" fill="white" font-size="36" font-family="Microsoft YaHei">会议室</text>
                    </svg>
                    <div class="image-overlay">
                        <div class="overlay-title">工作会议</div>
                        <div class="overlay-subtitle">重要部署会议</div>
                    </div>
                </div>

                <!-- 第三张图片 -->
                <div class="image-item" onclick="selectImage(2)">
                    <svg class="slide-image" viewBox="0 0 800 300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="800" height="300" fill="#27ae60"/>
                        <text x="400" y="150" text-anchor="middle" dominant-baseline="middle" fill="white" font-size="36" font-family="Microsoft YaHei">学习会议</text>
                    </svg>
                    <div class="image-overlay">
                        <div class="overlay-title">学习培训</div>
                        <div class="overlay-subtitle">专题学习会议</div>
                    </div>
                </div>

            <!-- 内容展示区域 -->
            <div class="content-area">
                <div class="content-description active">
                    区政府工作会议今天（2月8日）下午召开。区委副书记、区长蒋季茂出席，会议听取了各部门工作汇报，并对下一步工作进行了部署。会议要求，各部门要认真贯彻落实区委、区政府的决策部署，进一步提高工作效率，确保各项工作顺利推进。
                </div>
                <div class="content-description">
                    今日召开的重要工作会议，就当前各项重点工作进行了深入讨论和部署。与会人员就相关议题进行了充分交流，形成了一系列重要共识，为下一步工作指明了方向。
                </div>
                <div class="content-description">
                    专题学习会议在市政府会议室举行，全体工作人员参加了此次学习活动。会议围绕最新政策文件进行了深入学习和讨论，进一步提高了大家的理论水平和业务能力。
                </div>
        </div>
    </div>

    <script>
        let currentIndex = 0;
        const totalImages = 3;

        function selectImage(index) {
            // 移除所有active类
            document.querySelectorAll('.image-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.content-description').forEach(desc => {
                desc.classList.remove('active');
            });

            // 添加active类到选中的项
            document.querySelectorAll('.image-item')[index].classList.add('active');
            document.querySelectorAll('.content-description')[index].classList.add('active');

            currentIndex = index;
        }

        function nextImage() {
            const nextIndex = (currentIndex + 1) % totalImages;
            selectImage(nextIndex);
        }

        function previousImage() {
            const prevIndex = (currentIndex - 1 + totalImages) % totalImages;
            selectImage(prevIndex);
        }

        // 自动播放功能（可选）
        setInterval(() => {
            nextImage();
        }, 5000);
    </script>
</body>
</html>
