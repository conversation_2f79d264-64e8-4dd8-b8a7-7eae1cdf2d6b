<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>餐厅展示</title>
    <!-- 引入swiper -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #000;
            overflow: hidden;
        }

        .swiper-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .swiper-slide {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #1a1a1a;
        }

        .slide-left {
            flex: 1;
            max-width: 40%;
            height: 100%;
            position: relative;
        }

        .slide-right {
            flex: 1;
            max-width: 60%;
            height: 100vh;
            background-size: cover;
            background-position: center;
            overflow: hidden;
        }

        .content-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            background-size: cover;
            background-position: center;
        }

        .slide-content {
            color: white;
            z-index: 15;
            position: relative;
            padding: 40px 50px 40px 40px;
            text-align: right;
            margin-top: 30%;
        }

        .store-title {
            font-size: 48px;
            font-weight: bold;
            color: #D4AF37;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            letter-spacing: 2px;
        }

        .store-subtitle {
            font-size: 36px;
            color: white;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        .contact-info {
            font-size: 18px;
            line-height: 1.8;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }

        .contact-info div {
            margin-bottom: 8px;
        }

        /* 自定义导航按钮 */
        .swiper-button-prev,
        .swiper-button-next {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            transition: all 0.3s ease;
            z-index: 8;
            position: absolute;
            top: auto;
            bottom: 280px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .swiper-button-prev::after,
        .swiper-button-next::after {
            display: none;
        }

        .swiper-button-prev {
            left: calc(40% - 200px);
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yOSAxOUwyMSAyNUwyOSAzMSIgc3Ryb2tlPSIjNjY2NjY2IiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=');
        }

        .swiper-button-next {
            left: calc(40% - 120px);
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMSAxOUwyOSAyNUwyMSAzMSIgc3Ryb2tlPSIjNjY2NjY2IiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=');
        }

        .swiper-button-prev:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNDNDFFM0EiLz4KPHBhdGggZD0iTTI5IDE5TDIxIDI1TDI5IDMxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=');
            transform: scale(1.1);
        }

        .swiper-button-next:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNDNDFFM0EiLz4KPHBhdGggZD0iTTIxIDE5TDI5IDI1TDIxIDMxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=');
            transform: scale(1.1);
        }

        /* 底部装饰 */
        .bottom-decoration {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            z-index: 20;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bottom-decoration::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: linear-gradient(45deg, #C41E3A 0%, #8B0000 100%);
            border-radius: 50% 50% 0 0 / 100% 100% 0 0;
            transform: scaleX(1.1);
        }

        .logo-circle {
            width: 160px;
            height: 160px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            position: relative;
            z-index: 25;
            margin-top: -80px;
            overflow: hidden;
        }

        .logo-image {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
        }



        /* 分页器样式 */
        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 1;
        }

        .swiper-pagination-bullet-active {
            background: #C41E3A;
        }
    </style>
</head>
<body>
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <!-- 第一张幻灯片 - 川渝江湖风 -->
            <div class="swiper-slide">
                <div class="slide-left">
                    <div class="content-background" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyMCIgeTI9IjEwODAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzJEMUIxNCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM1QjM0MjYiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');"></div>
                    <div class="slide-content">
                        <div class="store-title">STORE STYLE</div>
                        <div class="store-subtitle">川渝江湖风</div>
                        <div class="contact-info">
                            <div>联系人：胡经理</div>
                            <div>联系电话：139-0000-0000</div>
                            <div>店铺地址：山西省太原市小店区清徐创新基地</div>
                        </div>
                    </div>
                </div>
                <div class="slide-right" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjEwODAiIHZpZXdCb3g9IjAgMCA4MDAgMTA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iODAwIiB5Mj0iMTA4MCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjOEIwMDAwIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iI0M0MUUzQSIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');"></div>
            </div>

            <!-- 第二张幻灯片 - 传统火锅 -->
            <div class="swiper-slide">
                <div class="slide-left">
                    <div class="content-background" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyMCIgeTI9IjEwODAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzNCMkYyQSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM2QjVCNDYiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');"></div>
                    <div class="slide-content">
                        <div class="store-title">TRADITIONAL</div>
                        <div class="store-subtitle">传统火锅文化</div>
                        <div class="contact-info">
                            <div>营业时间：10:00-22:00</div>
                            <div>特色菜品：麻辣火锅、清汤火锅</div>
                            <div>服务理念：传承经典，创新口味</div>
                        </div>
                    </div>
                </div>
                <div class="slide-right" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjEwODAiIHZpZXdCb3g9IjAgMCA4MDAgMTA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iODAwIiB5Mj0iMTA4MCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjMDA2NjAwIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzAwOEIwMCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');"></div>
            </div>

            <!-- 第三张幻灯片 - 环境展示 -->
            <div class="swiper-slide">
                <div class="slide-left">
                    <div class="content-background" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyMCIgeTI9IjEwODAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzNEMjcxOSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM3MDRGMzMiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');"></div>
                    <div class="slide-content">
                        <div class="store-title">ENVIRONMENT</div>
                        <div class="store-subtitle">舒适用餐环境</div>
                        <div class="contact-info">
                            <div>包间数量：8个豪华包间</div>
                            <div>大厅座位：可容纳120人同时用餐</div>
                            <div>停车位：免费停车位50个</div>
                        </div>
                    </div>
                </div>
                <div class="slide-right" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjEwODAiIHZpZXdCb3g9IjAgMCA4MDAgMTA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iODAwIiB5Mj0iMTA4MCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjNDY0NjQ2Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzY5Njk2OSIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');"></div>
            </div>
        </div>

        <!-- 导航按钮 -->
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>

        <!-- 分页器 -->
        <div class="swiper-pagination"></div>

        <!-- 底部装饰 -->
        <div class="bottom-decoration">
            <div class="logo-circle">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNjAiIHI9IjYwIiBmaWxsPSIjQzQxRTNBIi8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNjAiIHI9IjQ1IiBmaWxsPSJ3aGl0ZSIvPgo8IS0tIOeBq+mUhSAtLT4KPHN2ZyB4PSIzNSIgeT0iMzAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDUwIDYwIiBmaWxsPSJub25lIj4KPHN2ZyB4PSIxNSIgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDIwIDQwIiBmaWxsPSJub25lIj4KPGVsbGlwc2UgY3g9IjEwIiBjeT0iMzAiIHJ4PSI4IiByeT0iOCIgZmlsbD0iI0M0MUUzQSIvPgo8cmVjdCB4PSI4IiB5PSIxMCIgd2lkdGg9IjQiIGhlaWdodD0iMjAiIGZpbGw9IiNDNDFFM0EiLz4KPC9zdmc+CjwhLS0g54Gr6ZSF5oqK5omLIC0tPgo8c3ZnIHg9IjEwIiB5PSIxNSIgd2lkdGg9IjgiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA4IDMwIiBmaWxsPSJub25lIj4KPHJlY3QgeD0iMyIgeT0iNSIgd2lkdGg9IjIiIGhlaWdodD0iMjAiIGZpbGw9IiNDNDFFM0EiLz4KPC9zdmc+CjxzdmcgeD0iMzIiIHk9IjE1IiB3aWR0aD0iOCIgaGVpZ2h0PSIzMCIgdmlld0JveD0iMCAwIDggMzAiIGZpbGw9Im5vbmUiPgo8cmVjdCB4PSIzIiB5PSI1IiB3aWR0aD0iMiIgaGVpZ2h0PSIyMCIgZmlsbD0iI0M0MUUzQSIvPgo8L3N2Zz4KPC9zdmc+Cjwvc3ZnPgo=" alt="餐厅Logo" class="logo-image" />
            </div>
        </div>
    </div>

    <script>
        // 初始化Swiper
        const swiper = new Swiper('.swiper-container', {
            // 基本配置
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            speed: 800,
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },

            // 导航
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },

            // 分页器
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },

            // 键盘控制
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            },

            // 鼠标滚轮控制
            mousewheel: {
                invert: false,
            },

            // 触摸滑动
            touchRatio: 1,
            touchAngle: 45,
            grabCursor: true,

            // 响应式断点
            breakpoints: {
                768: {
                    slidesPerView: 1,
                },
                1024: {
                    slidesPerView: 1,
                }
            }
        });

        // 添加鼠标悬停暂停自动播放
        const swiperContainer = document.querySelector('.swiper-container');
        swiperContainer.addEventListener('mouseenter', () => {
            swiper.autoplay.stop();
        });
        swiperContainer.addEventListener('mouseleave', () => {
            swiper.autoplay.start();
        });
    </script>
</body>
</html>