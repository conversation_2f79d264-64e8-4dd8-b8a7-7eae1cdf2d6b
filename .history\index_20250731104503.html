<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>餐厅展示</title>
    <!-- 引入swiper -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #000;
            overflow: hidden;
        }

        .swiper-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .swiper-slide {
            position: relative;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding-left: 80px;
        }

        .slide-content {
            color: white;
            z-index: 10;
            max-width: 500px;
        }

        .store-title {
            font-size: 48px;
            font-weight: bold;
            color: #D4AF37;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            letter-spacing: 2px;
        }

        .store-subtitle {
            font-size: 36px;
            color: white;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        .contact-info {
            font-size: 18px;
            line-height: 1.8;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }

        .contact-info div {
            margin-bottom: 8px;
        }

        /* 自定义导航按钮 */
        .swiper-button-prev,
        .swiper-button-next {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.5);
            color: white;
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .swiper-button-prev:hover,
        .swiper-button-next:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.8);
        }

        .swiper-button-prev {
            left: 30px;
            background: #C41E3A;
            border-color: #C41E3A;
        }

        .swiper-button-next {
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .swiper-button-prev::after,
        .swiper-button-next::after {
            font-size: 16px;
            font-weight: bold;
        }

        /* 底部装饰 */
        .bottom-decoration {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: linear-gradient(45deg, #C41E3A 0%, #8B0000 100%);
            z-index: 20;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-circle {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #C41E3A;
        }

        /* 背景遮罩 */
        .slide-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                to right,
                rgba(0,0,0,0.7) 0%,
                rgba(0,0,0,0.4) 50%,
                rgba(0,0,0,0.2) 100%
            );
            z-index: 5;
        }

        /* 分页器样式 */
        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 1;
        }

        .swiper-pagination-bullet-active {
            background: #C41E3A;
        }
    </style>
</head>
<body>
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <!-- 第一张幻灯片 - 川渝江湖风 -->
            <div class="swiper-slide" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyMCIgeTI9IjEwODAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzJEMUIxNCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM1QjM0MjYiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');">
                <div class="slide-overlay"></div>
                <div class="slide-content">
                    <div class="store-title">STORE STYLE</div>
                    <div class="store-subtitle">川渝江湖风</div>
                    <div class="contact-info">
                        <div>联系人：胡经理</div>
                        <div>联系电话：139-0000-0000</div>
                        <div>店铺地址：山西省太原市小店区清徐创新基地</div>
                    </div>
                </div>
            </div>

            <!-- 第二张幻灯片 - 传统火锅 -->
            <div class="swiper-slide" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyMCIgeTI9IjEwODAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzNCMkYyQSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM2QjVCNDYiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');">
                <div class="slide-overlay"></div>
                <div class="slide-content">
                    <div class="store-title">TRADITIONAL</div>
                    <div class="store-subtitle">传统火锅文化</div>
                    <div class="contact-info">
                        <div>营业时间：10:00-22:00</div>
                        <div>特色菜品：麻辣火锅、清汤火锅</div>
                        <div>服务理念：传承经典，创新口味</div>
                    </div>
                </div>
            </div>

            <!-- 第三张幻灯片 - 环境展示 -->
            <div class="swiper-slide" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiB2aWV3Qm94PSIwIDAgMTkyMCAxMDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTkyMCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyMCIgeTI9IjEwODAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzNEMjcxOSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM3MDRGMzMiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K');">
                <div class="slide-overlay"></div>
                <div class="slide-content">
                    <div class="store-title">ENVIRONMENT</div>
                    <div class="store-subtitle">舒适用餐环境</div>
                    <div class="contact-info">
                        <div>包间数量：8个豪华包间</div>
                        <div>大厅座位：可容纳120人同时用餐</div>
                        <div>停车位：免费停车位50个</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航按钮 -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>

        <!-- 分页器 -->
        <div class="swiper-pagination"></div>

        <!-- 底部装饰 -->
        <div class="bottom-decoration">
            <div class="logo-circle">
                <div class="logo-text">川</div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Swiper
        const swiper = new Swiper('.swiper-container', {
            // 基本配置
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            speed: 800,
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },

            // 导航
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },

            // 分页器
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },

            // 键盘控制
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            },

            // 鼠标滚轮控制
            mousewheel: {
                invert: false,
            },

            // 触摸滑动
            touchRatio: 1,
            touchAngle: 45,
            grabCursor: true,

            // 响应式断点
            breakpoints: {
                768: {
                    slidesPerView: 1,
                },
                1024: {
                    slidesPerView: 1,
                }
            }
        });

        // 添加鼠标悬停暂停自动播放
        const swiperContainer = document.querySelector('.swiper-container');
        swiperContainer.addEventListener('mouseenter', () => {
            swiper.autoplay.stop();
        });
        swiperContainer.addEventListener('mouseleave', () => {
            swiper.autoplay.start();
        });
    </script>
</body>
</html>