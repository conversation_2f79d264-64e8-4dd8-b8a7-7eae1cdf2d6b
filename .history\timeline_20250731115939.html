<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蜿蜒时间轴</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
            min-height: 100vh;
            overflow-x: auto;
        }

        .timeline-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 50px 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI0MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDQwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyMDAiIGhlaWdodD0iNDAwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIgZmlsbC1vcGFjaXR5PSIwLjMiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8wXzEiIHgxPSIwIiB5MT0iMCIgeDI9IjEyMDAiIHkyPSI0MDAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iI2Y1ZjFlYiIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNlOGRkZDQiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K') center/cover;
        }

        .timeline-path {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI0MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDQwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTUwIDMwMEM1MCAzMDAgMTUwIDI1MCAzMDAgMjAwQzQ1MCA1MCA2MDAgMTAwIDc1MCAyMDBDOTAwIDMwMCAxMDUwIDI1MCAxMTUwIDIwMCIgc3Ryb2tlPSJ1cmwoI2dyYWRpZW50KSIgc3Ryb2tlLXdpZHRoPSIxNSIgZmlsbD0ibm9uZSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAiIHkxPSIwIiB4Mj0iMSIgeTI9IjAiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjOEI0NTEzIi8+CjxzdG9wIG9mZnNldD0iMjUlIiBzdG9wLWNvbG9yPSIjQTY2MjI5Ii8+CjxzdG9wIG9mZnNldD0iNTAlIiBzdG9wLWNvbG9yPSIjQ0Q4NTNGIi8+CjxzdG9wIG9mZnNldD0iNzUlIiBzdG9wLWNvbG9yPSIjREFBNTIwIi8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI0Y0QTQ2MCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=') center/cover no-repeat;
        }

        .timeline-event {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
        }

        .event-dot {
            width: 16px;
            height: 16px;
            background: #C41E3A;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            margin-bottom: 10px;
        }

        .event-card {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
            min-width: 180px;
            max-width: 220px;
            position: relative;
        }

        .event-card::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid white;
        }

        .event-date {
            color: #C41E3A;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .event-date::before {
            content: '●';
            margin-right: 6px;
            font-size: 12px;
        }

        .event-title {
            color: #333;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 4px;
        }

        .event-desc {
            color: #666;
            font-size: 12px;
            line-height: 1.3;
        }

        /* 事件位置定位 */
        .event-1 {
            left: 50px;
            top: 250px;
        }

        .event-2 {
            left: 250px;
            top: 120px;
        }

        .event-3 {
            left: 450px;
            top: 280px;
        }

        .event-4 {
            left: 650px;
            top: 80px;
        }

        .event-5 {
            left: 850px;
            top: 240px;
        }

        .event-6 {
            left: 1050px;
            top: 120px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .timeline-container {
                height: 300px;
                margin: 20px 0;
            }
            
            .event-card {
                min-width: 150px;
                max-width: 180px;
                padding: 12px 15px;
            }
            
            .event-date {
                font-size: 12px;
            }
            
            .event-title {
                font-size: 11px;
            }
            
            .event-desc {
                font-size: 10px;
            }
        }

        /* 动画效果 */
        .timeline-event {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        .event-1 { animation-delay: 0.1s; }
        .event-2 { animation-delay: 0.2s; }
        .event-3 { animation-delay: 0.3s; }
        .event-4 { animation-delay: 0.4s; }
        .event-5 { animation-delay: 0.5s; }
        .event-6 { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .event-dot {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 2px 8px rgba(0,0,0,0.2), 0 0 0 0 rgba(196, 30, 58, 0.4);
            }
            70% {
                box-shadow: 0 2px 8px rgba(0,0,0,0.2), 0 0 0 10px rgba(196, 30, 58, 0);
            }
            100% {
                box-shadow: 0 2px 8px rgba(0,0,0,0.2), 0 0 0 0 rgba(196, 30, 58, 0);
            }
        }
    </style>
</head>
<body>
    <div class="timeline-container">
        <div class="timeline-path"></div>
        
        <!-- 2019年3月20日 -->
        <div class="timeline-event event-1">
            <div class="event-card">
                <div class="event-date">2019年3月20日</div>
                <div class="event-title">公司成立暨战略发布会</div>
                <div class="event-desc">正式成立，发布企业愿景</div>
            </div>
            <div class="event-dot"></div>
        </div>

        <!-- 2020年3月20日 -->
        <div class="timeline-event event-2">
            <div class="event-card">
                <div class="event-date">2020年3月20日</div>
                <div class="event-title">一周年庆典活动</div>
                <div class="event-desc">庆祝一周年发展成果</div>
            </div>
            <div class="event-dot"></div>
        </div>

        <!-- 2021年3月20日 -->
        <div class="timeline-event event-3">
            <div class="event-card">
                <div class="event-date">2021年3月20日</div>
                <div class="event-title">中国区分公司成立</div>
                <div class="event-desc">业务拓展至中国市场</div>
            </div>
            <div class="event-dot"></div>
        </div>

        <!-- 2022年3月20日 -->
        <div class="timeline-event event-4">
            <div class="event-card">
                <div class="event-date">2022年3月20日</div>
                <div class="event-title">海外业务全面启动</div>
                <div class="event-desc">拓展海外市场，建立全球业务网络</div>
            </div>
            <div class="event-dot"></div>
        </div>

        <!-- 2023年3月20日 -->
        <div class="timeline-event event-5">
            <div class="event-card">
                <div class="event-date">2023年3月20日</div>
                <div class="event-title">技术创新突破年</div>
                <div class="event-desc">核心技术获得重大突破，产品升级</div>
            </div>
            <div class="event-dot"></div>
        </div>

        <!-- 2025年3月20日 -->
        <div class="timeline-event event-6">
            <div class="event-card">
                <div class="event-date">2025年3月20日</div>
                <div class="event-title">2025战略规划发布</div>
                <div class="event-desc">发布未来五年发展战略，开启新征程</div>
            </div>
            <div class="event-dot"></div>
        </div>
    </div>
</body>
</html>
