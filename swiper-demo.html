<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swiper 轮播图</title>
    <!-- 引入swiper -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .swiper-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .swiper-slide {
            position: relative;
        }

        .slide-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            display: block;
        }

        .slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            padding: 40px 20px 20px;
        }

        .slide-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .slide-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .slide-description {
            margin-top: 20px;
            padding: 20px;
            background: white;
            color: #333;
            font-size: 14px;
            line-height: 1.6;
        }

        /* 自定义导航按钮 */
        .swiper-button-prev,
        .swiper-button-next {
            width: 40px;
            height: 40px;
            margin-top: -20px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .swiper-button-prev {
            left: 20px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMjQgMTJMMTYgMjBMMjQgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        }

        .swiper-button-next {
            right: 20px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9InJnYmEoMCwwLDAsMC41KSIvPgo8cGF0aCBkPSJNMTYgMTJMMjQgMjBMMTYgMjgiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
        }

        /* 悬停效果 */
        .swiper-button-prev:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwMDc4ZDQiLz4KPHBhdGggZD0iTTI0IDEyTDE2IDIwTDI0IDI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }

        .swiper-button-next:hover {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMwMDc4ZDQiLz4KPHBhdGggZD0iTTE2IDEyTDI0IDIwTDE2IDI4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K');
        }

        /* 隐藏默认的箭头 */
        .swiper-button-prev::after,
        .swiper-button-next::after {
            display: none;
        }

        /* 隐藏分页圆点 */
        .swiper-pagination {
            display: none !important;
        }

        .title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1 class="title">新闻轮播</h1>
    
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <!-- 第一张幻灯片 -->
            <div class="swiper-slide">
                <img src="https://via.placeholder.com/800x300/4a90e2/ffffff?text=人民政协" alt="人民政协" class="slide-image">
                <div class="slide-content">
                    <div class="slide-title">蒋季茂</div>
                    <div class="slide-subtitle">市委副书记、市长</div>
                </div>
                <div class="slide-description">
                    区政府工作会议今天（2月8日）下午召开。区委副书记、区长蒋季茂出席，会议听取了各部门工作汇报，并对下一步工作进行了部署。会议要求，各部门要认真贯彻落实区委、区政府的决策部署，进一步提高工作效率，确保各项工作顺利推进。
                </div>
            </div>
            
            <!-- 第二张幻灯片 -->
            <div class="swiper-slide">
                <img src="https://via.placeholder.com/800x300/5cb85c/ffffff?text=会议室" alt="会议室" class="slide-image">
                <div class="slide-content">
                    <div class="slide-title">工作会议</div>
                    <div class="slide-subtitle">重要部署会议</div>
                </div>
                <div class="slide-description">
                    今日召开的重要工作会议，就当前各项重点工作进行了深入讨论和部署。与会人员就相关议题进行了充分交流，形成了一系列重要共识，为下一步工作指明了方向。
                </div>
            </div>
            
            <!-- 第三张幻灯片 -->
            <div class="swiper-slide">
                <img src="https://via.placeholder.com/800x300/d9534f/ffffff?text=学习会议" alt="学习会议" class="slide-image">
                <div class="slide-content">
                    <div class="slide-title">学习培训</div>
                    <div class="slide-subtitle">专题学习会议</div>
                </div>
                <div class="slide-description">
                    专题学习会议在市政府会议室举行，全体工作人员参加了此次学习活动。会议围绕最新政策文件进行了深入学习和讨论，进一步提高了大家的理论水平和业务能力。
                </div>
            </div>
        </div>
        
        <!-- 导航按钮 -->
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
    </div>

    <script>
        // 初始化Swiper
        const swiper = new Swiper('.swiper-container', {
            // 基本配置
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            
            // 导航按钮
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            
            // 切换效果
            effect: 'slide',
            speed: 600,
            
            // 响应式断点
            breakpoints: {
                640: {
                    slidesPerView: 1,
                },
                768: {
                    slidesPerView: 1,
                },
                1024: {
                    slidesPerView: 1,
                },
            },
        });
    </script>
</body>
</html>
